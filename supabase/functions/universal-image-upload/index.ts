import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.39.8";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
};

interface UploadMetadata {
  bucketName: string;
  folderPath: string;
  fileName?: string;
  uploadType: 'avatar' | 'property' | 'inventory' | 'damage' | 'general';
  entityId?: string;
  caption?: string;
  [key: string]: any;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  if (req.method !== 'POST') {
    return new Response(
      JSON.stringify({ error: 'Method not allowed' }),
      { 
        status: 405, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );
  }

  try {
    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Parse form data
    const formData = await req.formData();
    const file = formData.get('file') as File;
    const metadataJson = formData.get('metadata') as string;

    if (!file) {
      return new Response(
        JSON.stringify({ error: 'No file provided' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    if (!metadataJson) {
      return new Response(
        JSON.stringify({ error: 'No metadata provided' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    const metadata: UploadMetadata = JSON.parse(metadataJson);
    const { bucketName, folderPath, fileName, uploadType, entityId } = metadata;

    // Validate required fields
    if (!bucketName || !folderPath || !uploadType) {
      return new Response(
        JSON.stringify({ error: 'Missing required metadata fields' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    // Get user from JWT token
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      return new Response(
        JSON.stringify({ error: 'No authorization header' }),
        { 
          status: 401, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    const { data: { user }, error: authError } = await supabase.auth.getUser(
      authHeader.replace('Bearer ', '')
    );

    if (authError || !user) {
      return new Response(
        JSON.stringify({ error: 'Unauthorized' }),
        { 
          status: 401, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    // Generate unique filename if not provided
    const timestamp = new Date().getTime();
    const randomString = Math.random().toString(36).substring(2, 10);
    const fileExtension = file.name.split('.').pop()?.toLowerCase() || 'jpg';
    const finalFileName = fileName || `${timestamp}-${randomString}.${fileExtension}`;
    const filePath = `${folderPath}/${finalFileName}`;

    console.log(`Processing ${uploadType} upload for user ${user.id}: ${filePath}`);

    // Ensure bucket exists
    const { data: buckets } = await supabase.storage.listBuckets();
    if (!buckets?.find(b => b.name === bucketName)) {
      const { error: bucketError } = await supabase.storage.createBucket(bucketName, {
        public: true,
        fileSizeLimit: 10485760, // 10MB
      });
      
      if (bucketError) {
        console.error('Error creating bucket:', bucketError);
        return new Response(
          JSON.stringify({ error: 'Failed to initialize storage' }),
          { 
            status: 500, 
            headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
          }
        );
      }
    }

    // Upload file to storage
    const { error: uploadError } = await supabase.storage
      .from(bucketName)
      .upload(filePath, file, {
        contentType: file.type,
        upsert: true,
      });

    if (uploadError) {
      console.error('Storage upload error:', uploadError);
      return new Response(
        JSON.stringify({ error: 'Failed to upload file', details: uploadError }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    // Get public URL
    const { data: publicUrlData } = supabase.storage
      .from(bucketName)
      .getPublicUrl(filePath);

    if (!publicUrlData?.publicUrl) {
      return new Response(
        JSON.stringify({ error: 'Failed to get public URL' }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    // Handle database updates based on upload type
    try {
      switch (uploadType) {
        case 'avatar':
          if (entityId) {
            await supabase
              .from('profiles')
              .update({ avatar_url: publicUrlData.publicUrl })
              .eq('id', entityId);
          }
          break;

        case 'property':
          if (entityId) {
            await supabase
              .from('properties')
              .update({ image_url: publicUrlData.publicUrl })
              .eq('id', entityId)
              .eq('user_id', user.id);
          }
          break;

        case 'inventory':
          if (entityId) {
            await supabase
              .from('inventory_items')
              .update({ image_url: publicUrlData.publicUrl })
              .eq('id', entityId)
              .eq('user_id', user.id);
          }
          break;

        case 'damage':
          if (entityId) {
            // Insert into damage_photos table
            await supabase
              .from('damage_photos')
              .insert({
                damage_report_id: entityId,
                user_id: user.id,
                file_name: finalFileName,
                file_path: filePath,
                caption: metadata.caption || null,
                url: publicUrlData.publicUrl,
              });

            // Log activity
            await supabase
              .from('damage_report_activities')
              .insert({
                damage_report_id: entityId,
                user_id: user.id,
                action: 'upload',
                details: `Uploaded photo: ${finalFileName}`,
              });
          }
          break;

        case 'general':
          // No database update needed for general uploads
          break;

        default:
          console.warn(`Unknown upload type: ${uploadType}`);
      }
    } catch (dbError) {
      console.error('Database update error:', dbError);
      // Don't fail the upload if database update fails
      // The file is already uploaded successfully
    }

    console.log(`Upload successful: ${publicUrlData.publicUrl}`);

    return new Response(
      JSON.stringify({
        success: true,
        url: publicUrlData.publicUrl,
        filePath,
        fileName: finalFileName,
        uploadType,
      }),
      { 
        status: 200, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );

  } catch (error) {
    console.error('Upload error:', error);
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error', 
        details: error instanceof Error ? error.message : 'Unknown error' 
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );
  }
});
