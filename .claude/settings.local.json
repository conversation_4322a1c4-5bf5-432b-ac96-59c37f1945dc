{"permissions": {"allow": ["Bash(rm:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(npm run dev:local:*)", "Bash(npm run supabase:status:*)", "Bash(npm run supabase:start:*)", "Bash(npm run dev:remote:*)", "Bash(npm run test:*)", "Bash(npm run lint)", "Bash(find:*)", "Bash(npm run build:*)", "<PERSON><PERSON>(sed:*)", "Bash(cp:*)", "<PERSON><PERSON>(npx playwright test:*)", "Bash(npx:*)", "Bash(node:*)", "Bash(npm install:*)", "<PERSON><PERSON>(curl:*)", "mcp__ide__getDiagnostics", "Bash(grep:*)", "<PERSON><PERSON>(cat:*)", "Bash(npm run preview:*)", "<PERSON><PERSON>(pkill:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(git checkout:*)", "Bash(ls:*)", "WebFetch(domain:docs.anthropic.com)", "Bash(npm uninstall:*)", "Bash(git reset:*)", "Bash(git push:*)"], "deny": []}}