// Universal Image Processing Utilities for StayFuse
// Supports WebP conversion, optimized compression, and standardized dimensions

// Add a global type declaration for the bucket name and folder path
declare global {
  interface Window {
    INVENTORY_BUCKET_NAME?: string;
    INVENTORY_FOLDER_PATH?: string;
  }
}

import { supabase } from '@/integrations/supabase/client';

// Updated constants for improved image processing
const MAX_IMAGE_WIDTH = 1920; // Max width in pixels
const MAX_IMAGE_HEIGHT = 1080; // Max height in pixels
const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB in bytes
const WEBP_QUALITY = 0.85; // WebP compression quality (0.0 - 1.0)
const JPEG_QUALITY = 0.8; // JPEG fallback compression quality (0.0 - 1.0)

// Supported image formats
const SUPPORTED_IMAGE_TYPES = [
  'image/jpeg',
  'image/jpg',
  'image/png',
  'image/webp',
  'image/heic',
  'image/heif'
];

// File type validation
export function validateImageFile(file: File): { isValid: boolean; error?: string } {
  if (!file) {
    return { isValid: false, error: 'No file provided' };
  }

  if (!SUPPORTED_IMAGE_TYPES.includes(file.type)) {
    return {
      isValid: false,
      error: `Unsupported file type: ${file.type}. Supported types: JPEG, PNG, WebP, HEIC`
    };
  }

  if (file.size > MAX_FILE_SIZE) {
    return {
      isValid: false,
      error: `File size too large: ${(file.size / 1024 / 1024).toFixed(1)}MB. Maximum allowed: 10MB`
    };
  }

  return { isValid: true };
}

// Check if browser supports WebP
function supportsWebP(): Promise<boolean> {
  return new Promise((resolve) => {
    const canvas = document.createElement('canvas');
    canvas.width = 1;
    canvas.height = 1;
    canvas.toBlob((blob) => {
      resolve(blob !== null);
    }, 'image/webp');
  });
}

/**
 * Calculates optimal dimensions while maintaining aspect ratio
 * @param originalWidth Original image width
 * @param originalHeight Original image height
 * @param maxWidth Maximum allowed width
 * @param maxHeight Maximum allowed height
 * @returns Object with new width and height
 */
function calculateOptimalDimensions(
  originalWidth: number,
  originalHeight: number,
  maxWidth: number,
  maxHeight: number
): { width: number; height: number } {
  let width = originalWidth;
  let height = originalHeight;

  // Calculate aspect ratio
  const aspectRatio = width / height;

  // If image is larger than max dimensions, scale it down
  if (width > maxWidth || height > maxHeight) {
    if (aspectRatio > maxWidth / maxHeight) {
      // Width is the limiting factor
      width = maxWidth;
      height = width / aspectRatio;
    } else {
      // Height is the limiting factor
      height = maxHeight;
      width = height * aspectRatio;
    }
  }

  return {
    width: Math.round(width),
    height: Math.round(height)
  };
}

/**
 * Universal image processing function with WebP support and optimized compression
 * @param file The image file to process
 * @param options Processing options
 * @returns Promise that resolves with the processed image as a Blob
 */
export async function processImageFile(
  file: File,
  options: {
    maxWidth?: number;
    maxHeight?: number;
    quality?: number;
    forceFormat?: 'webp' | 'jpeg';
  } = {}
): Promise<{ blob: Blob; format: string; originalSize: number; processedSize: number }> {
  // Validate file first
  const validation = validateImageFile(file);
  if (!validation.isValid) {
    throw new Error(validation.error);
  }

  const {
    maxWidth = MAX_IMAGE_WIDTH,
    maxHeight = MAX_IMAGE_HEIGHT,
    quality,
    forceFormat
  } = options;

  // Check WebP support
  const webpSupported = await supportsWebP();
  const useWebP = forceFormat === 'webp' || (forceFormat !== 'jpeg' && webpSupported);
  const outputFormat = useWebP ? 'image/webp' : 'image/jpeg';
  const outputQuality = quality || (useWebP ? WEBP_QUALITY : JPEG_QUALITY);

  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);

    reader.onload = (event) => {
      const img = new Image();
      img.src = event.target?.result as string;

      img.onload = () => {
        try {
          const canvas = document.createElement('canvas');
          const ctx = canvas.getContext('2d');

          if (!ctx) {
            return reject(new Error('Could not get canvas context'));
          }

          // Calculate optimal dimensions
          const { width, height } = calculateOptimalDimensions(
            img.width,
            img.height,
            maxWidth,
            maxHeight
          );

          canvas.width = width;
          canvas.height = height;

          // Enable image smoothing for better quality
          ctx.imageSmoothingEnabled = true;
          ctx.imageSmoothingQuality = 'high';

          // Draw the resized image
          ctx.drawImage(img, 0, 0, width, height);

          // Convert to blob with appropriate format
          canvas.toBlob(
            (blob) => {
              if (blob) {
                resolve({
                  blob,
                  format: outputFormat,
                  originalSize: file.size,
                  processedSize: blob.size
                });
              } else {
                reject(new Error('Canvas toBlob failed'));
              }
            },
            outputFormat,
            outputQuality
          );
        } catch (error) {
          reject(new Error(`Image processing failed: ${error}`));
        }
      };

      img.onerror = () => {
        reject(new Error('Failed to load image'));
      };
    };

    reader.onerror = () => {
      reject(new Error('Failed to read file'));
    };
  });
}

/**
 * Legacy function - Downloads an image from a URL, compresses it, and uploads it to Supabase storage
 * @deprecated Use processImageFile and universal upload system instead
 * Handles base64 data URLs by converting them to files and uploading to Supabase
 */
export async function processAndUploadImage(
  imageUrl: string,
  bucketName: string = 'inventory',
  folderPath: string = 'inventory-images',
  fileName?: string
): Promise<string> {
  // Check if the URL is a base64 data URL
  if (imageUrl.startsWith('data:image')) {
    try {
      console.log('Processing base64 data URL with legacy function');

      // Convert base64 to blob
      const response = await fetch(imageUrl);
      const originalBlob = await response.blob();

      // Create a File object from the blob for processing
      const originalFile = new File([originalBlob], fileName || 'image.jpg', { type: originalBlob.type });

      // Use the new processing function
      const { blob: processedBlob, format } = await processImageFile(originalFile);

      // Generate a unique filename if not provided
      const timestamp = new Date().getTime();
      const randomString = Math.random().toString(36).substring(2, 10);
      const fileExtension = format === 'image/webp' ? 'webp' : 'jpg';
      const generatedFileName = fileName ?
        fileName.replace(/\.[^/.]+$/, `.${fileExtension}`) :
        `${timestamp}-${randomString}.${fileExtension}`;
      const filePath = `${folderPath}/${generatedFileName}`;

      // Upload to Supabase storage
      const { data, error } = await supabase.storage
        .from(bucketName)
        .upload(filePath, processedBlob, {
          contentType: format,
          upsert: true
        });

      if (error) {
        console.error('Error uploading processed image to storage:', error);
        return imageUrl; // Return original URL on error
      }

      // Get the public URL
      const { data: publicUrlData } = supabase.storage
        .from(bucketName)
        .getPublicUrl(filePath);

      console.log('Base64 image uploaded successfully:', publicUrlData.publicUrl);
      return publicUrlData.publicUrl;
    } catch (error) {
      console.error('Error processing base64 image:', error);
      return imageUrl; // Return original URL on error
    }
  }

  // For non-base64 URLs, just return the original URL
  return imageUrl;
}

/**
 * Backward compatibility function for existing code
 * @deprecated Use processImageFile instead
 */
export async function resizeAndCompressImage(
  file: File,
  maxWidth: number,
  maxHeight: number,
  quality: number
): Promise<Blob> {
  console.warn('resizeAndCompressImage is deprecated. Use processImageFile instead.');
  const result = await processImageFile(file, {
    maxWidth,
    maxHeight,
    quality,
    forceFormat: 'jpeg'
  });
  return result.blob;
}

/**
 * Creates inventory buckets if they don't exist
 * Simplified version that just returns true to prevent blocking the dashboard
 */
export async function createInventoryBuckets(): Promise<boolean> {
  try {
    // Set global variables for bucket names
    window.INVENTORY_BUCKET_NAME = 'inventory';
    window.INVENTORY_FOLDER_PATH = 'inventory-images';

    // For dashboard performance, just return true
    return true;
  } catch (error) {
    console.error('Error checking inventory buckets:', error);
    return false;
  }
}

/**
 * Utility function to generate unique filename with proper extension
 */
export function generateUniqueFileName(originalName: string, format: string): string {
  const timestamp = new Date().getTime();
  const randomString = Math.random().toString(36).substring(2, 10);
  const extension = format === 'image/webp' ? 'webp' : 'jpg';

  // Remove original extension and add new one
  const baseName = originalName.replace(/\.[^/.]+$/, '');
  return `${baseName}-${timestamp}-${randomString}.${extension}`;
}

/**
 * Get file size in human readable format
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}