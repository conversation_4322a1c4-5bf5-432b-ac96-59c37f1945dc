import { useState, useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'sonner';
import { processImageFile, validateImageFile, generateUniqueFileName, formatFileSize } from '@/utils/imageProcessing';

export interface UploadConfig {
  bucketName: string;
  folderPath: string;
  fileName?: string;
  metadata?: Record<string, any>;
  onProgress?: (progress: number) => void;
  maxWidth?: number;
  maxHeight?: number;
  quality?: number;
  forceFormat?: 'webp' | 'jpeg';
}

export interface UploadResult {
  url: string | null;
  error: string | null;
  isUploading: boolean;
  progress: number;
  originalSize?: number;
  processedSize?: number;
  compressionRatio?: number;
}

export interface UploadResponse {
  success: boolean;
  url?: string;
  error?: string;
  metadata?: {
    originalSize: number;
    processedSize: number;
    format: string;
    dimensions: { width: number; height: number };
  };
}

/**
 * Determines upload type based on bucket name
 */
function determineUploadType(bucketName: string): 'avatar' | 'property' | 'inventory' | 'damage' | 'general' {
  switch (bucketName) {
    case 'avatar-images':
      return 'avatar';
    case 'property-images':
      return 'property';
    case 'inventory':
      return 'inventory';
    case 'damage-photos':
      return 'damage';
    default:
      return 'general';
  }
}

/**
 * Universal image upload hook that handles all image upload scenarios in the app
 * Supports WebP conversion, compression, resizing, and progress tracking
 */
export function useUniversalImageUpload() {
  const [uploadState, setUploadState] = useState<UploadResult>({
    url: null,
    error: null,
    isUploading: false,
    progress: 0,
  });

  const { authState } = useAuth();
  const userId = authState?.user?.id;

  const uploadImage = useCallback(async (
    file: File,
    config: UploadConfig
  ): Promise<UploadResponse> => {
    if (!userId) {
      const error = 'User not authenticated';
      setUploadState(prev => ({ ...prev, error }));
      return { success: false, error };
    }

    // Reset state
    setUploadState({
      url: null,
      error: null,
      isUploading: true,
      progress: 0,
    });

    try {
      // Validate file
      const validation = validateImageFile(file);
      if (!validation.isValid) {
        throw new Error(validation.error);
      }

      config.onProgress?.(10);
      setUploadState(prev => ({ ...prev, progress: 10 }));

      // Process image
      const processingResult = await processImageFile(file, {
        maxWidth: config.maxWidth,
        maxHeight: config.maxHeight,
        quality: config.quality,
        forceFormat: config.forceFormat,
      });

      config.onProgress?.(40);
      setUploadState(prev => ({ 
        ...prev, 
        progress: 40,
        originalSize: processingResult.originalSize,
        processedSize: processingResult.processedSize,
        compressionRatio: Math.round((1 - processingResult.processedSize / processingResult.originalSize) * 100)
      }));

      // Generate unique filename
      const fileName = config.fileName || generateUniqueFileName(file.name, processingResult.format);
      const filePath = `${config.folderPath}/${fileName}`;

      config.onProgress?.(60);
      setUploadState(prev => ({ ...prev, progress: 60 }));

      // Prepare metadata for edge function
      const uploadMetadata = {
        bucketName: config.bucketName,
        folderPath: config.folderPath,
        fileName: fileName,
        uploadType: determineUploadType(config.bucketName),
        entityId: config.metadata?.propertyId || config.metadata?.inventoryItemId || config.metadata?.damageReportId || userId,
        caption: config.metadata?.caption,
        ...config.metadata,
      };

      // Create form data for edge function
      const formData = new FormData();
      formData.append('file', new File([processingResult.blob], fileName, { type: processingResult.format }));
      formData.append('metadata', JSON.stringify(uploadMetadata));

      config.onProgress?.(70);
      setUploadState(prev => ({ ...prev, progress: 70 }));

      // Upload via edge function
      const { data: functionData, error: functionError } = await supabase.functions
        .invoke('universal-image-upload', {
          body: formData,
        });

      if (functionError) {
        throw new Error(`Upload failed: ${functionError.message}`);
      }

      if (!functionData?.success || !functionData?.url) {
        throw new Error('Upload failed: Invalid response from server');
      }

      config.onProgress?.(100);
      setUploadState({
        url: functionData.url,
        error: null,
        isUploading: false,
        progress: 100,
        originalSize: processingResult.originalSize,
        processedSize: processingResult.processedSize,
        compressionRatio: Math.round((1 - processingResult.processedSize / processingResult.originalSize) * 100)
      });

      // Show success message with compression info
      const compressionInfo = processingResult.processedSize < processingResult.originalSize
        ? ` (${formatFileSize(processingResult.originalSize)} → ${formatFileSize(processingResult.processedSize)})`
        : '';

      toast.success(`Image uploaded successfully${compressionInfo}`);

      return {
        success: true,
        url: functionData.url,
        metadata: {
          originalSize: processingResult.originalSize,
          processedSize: processingResult.processedSize,
          format: processingResult.format,
          dimensions: { width: 0, height: 0 }, // TODO: Extract from canvas
        },
      };

    } catch (error: any) {
      const errorMessage = error.message || 'Upload failed';
      setUploadState({
        url: null,
        error: errorMessage,
        isUploading: false,
        progress: 0,
      });

      toast.error(`Upload failed: ${errorMessage}`);
      console.error('Upload error:', error);

      return { success: false, error: errorMessage };
    }
  }, [userId]);

  const resetUploadState = useCallback(() => {
    setUploadState({
      url: null,
      error: null,
      isUploading: false,
      progress: 0,
    });
  }, []);

  return {
    uploadImage,
    resetUploadState,
    ...uploadState,
  };
}

/**
 * Specialized hook for profile avatar uploads
 */
export function useAvatarUpload() {
  const universalUpload = useUniversalImageUpload();
  const { authState } = useAuth();

  const uploadAvatar = useCallback(async (file: File) => {
    return universalUpload.uploadImage(file, {
      bucketName: 'avatar-images',
      folderPath: authState?.user?.id || 'temp',
      fileName: `avatar-${Date.now()}`,
      maxWidth: 512,
      maxHeight: 512,
    });
  }, [universalUpload, authState?.user?.id]);

  return {
    ...universalUpload,
    uploadAvatar,
  };
}

/**
 * Specialized hook for property image uploads
 */
export function usePropertyImageUpload() {
  const universalUpload = useUniversalImageUpload();
  
  const uploadPropertyImage = useCallback(async (file: File, propertyId: string) => {
    return universalUpload.uploadImage(file, {
      bucketName: 'property-images',
      folderPath: propertyId,
      metadata: { propertyId },
    });
  }, [universalUpload]);

  return {
    ...universalUpload,
    uploadPropertyImage,
  };
}

/**
 * Specialized hook for inventory image uploads
 */
export function useInventoryImageUpload() {
  const universalUpload = useUniversalImageUpload();
  
  const uploadInventoryImage = useCallback(async (file: File, inventoryItemId: string) => {
    return universalUpload.uploadImage(file, {
      bucketName: 'inventory',
      folderPath: `inventory-images/${inventoryItemId}`,
      metadata: { inventoryItemId },
    });
  }, [universalUpload]);

  return {
    ...universalUpload,
    uploadInventoryImage,
  };
}

/**
 * Specialized hook for damage report photo uploads
 */
export function useDamagePhotoUpload() {
  const universalUpload = useUniversalImageUpload();
  
  const uploadDamagePhoto = useCallback(async (file: File, damageReportId: string, caption?: string) => {
    return universalUpload.uploadImage(file, {
      bucketName: 'damage-photos',
      folderPath: damageReportId,
      metadata: { damageReportId, caption },
    });
  }, [universalUpload]);

  return {
    ...universalUpload,
    uploadDamagePhoto,
  };
}
