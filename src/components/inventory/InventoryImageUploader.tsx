import React from 'react';
import { UniversalImageUploader } from '@/components/common/UniversalImageUploader';
import { toast } from 'sonner';

interface InventoryImageUploaderProps {
  imageUrl: string;
  onImageChange: (url: string) => void;
  inventoryItemId?: string;
}

const InventoryImageUploader: React.FC<InventoryImageUploaderProps> = ({
  imageUrl,
  onImageChange,
  inventoryItemId
}) => {
  const handleUploadComplete = (url: string) => {
    onImageChange(url);
    toast.success('Inventory image uploaded successfully');
  };

  const handleUploadError = (error: string) => {
    toast.error(`Failed to upload image: ${error}`);
  };

  const handleRemoveImage = () => {
    onImageChange('');
    toast.success('Image removed');
  };

  const handleFileChange = useCallback(async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file || !userId) return;

    setIsUploading(true);
    try {
      // Use the updated processAndUploadImage which now handles resizing/compression
      const uploadedUrl = await processAndUploadImage(URL.createObjectURL(file), 'inventory', 'inventory-images', file.name);

      if (uploadedUrl) {
        onImageChange(uploadedUrl);
        toast.success('Image uploaded successfully');
      } else {
        throw new Error('Image upload failed');
      }
    } catch (error) {
      console.error('Error uploading image:', error);
      toast.error(`Error uploading image: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsUploading(false);
    }
  }, [inventoryItemId, userId, onImageChange]);

  const removeImage = useCallback(() => {
    onImageChange('');
  }, [onImageChange]);

  return (
    <div className="space-y-2">
      <label className="block text-sm font-medium">Item Image</label>

      {imageUrl ? (
        <div className="relative rounded-lg overflow-hidden border h-48">
          <img
            src={imageUrl}
            alt="Inventory Item"
            className="w-full h-full object-cover"
            onError={(e) => {
              console.error("Image failed to load:", imageUrl);

              // Check if it's a Supabase URL
              const isSupabaseUrl = imageUrl?.includes('supabase.co/storage/v1/object/public');
              if (isSupabaseUrl) {
                console.warn('Supabase storage URL failed to load:', imageUrl);

                // Try to fix common issues with Supabase URLs
                try {
                  // Try to use the URL directly without the Supabase storage prefix
                  const urlParts = imageUrl.split('/storage/v1/object/public/');
                  if (urlParts.length > 1) {
                    const directUrl = `${urlParts[0]}/storage/v1/object/public/${urlParts[1]}`;
                    console.log('Trying direct URL:', directUrl);
                    e.currentTarget.src = directUrl;
                    return; // Exit early to give the new URL a chance to load
                  }
                } catch (urlError) {
                  console.error('Error fixing Supabase URL:', urlError);
                }
              }

              // Check if it's an Amazon URL
              const isAmazonUrl = imageUrl?.includes('amazon.com') || imageUrl?.includes('amazon.co');
              if (isAmazonUrl) {
                console.warn('Amazon URL failed to load:', imageUrl);
                // Try to use a direct Amazon image URL
                try {
                  // Extract the ASIN from the Amazon URL
                  const asinMatch = imageUrl.match(/\/([A-Z0-9]{10})(\/|\?|$)/);
                  if (asinMatch && asinMatch[1]) {
                    const asin = asinMatch[1];
                    const amazonImageUrl = `https://m.media-amazon.com/images/I/${asin}.jpg`;
                    console.log('Trying Amazon image URL:', amazonImageUrl);
                    e.currentTarget.src = amazonImageUrl;
                    return; // Exit early to give the new URL a chance to load
                  }
                } catch (urlError) {
                  console.error('Error creating Amazon image URL:', urlError);
                }
              }

              e.currentTarget.src = "/placeholder.svg";
            }}
            crossOrigin="anonymous"
          />
          <div className="absolute bottom-2 right-2 flex gap-2">
            <Button
              variant="destructive"
              size="icon"
              onClick={removeImage}
              className="h-8 w-8 rounded-full bg-white/80 hover:bg-white text-red-500"
            >
              <Trash className="h-4 w-4" />
            </Button>
            <label className="h-8 w-8 flex items-center justify-center rounded-full bg-white/80 hover:bg-white text-primary cursor-pointer">
              <Upload className="h-4 w-4" />
              <input
                type="file"
                accept="image/*"
                className="hidden"
                onChange={handleFileChange}
                disabled={isUploading}
              />
            </label>
          </div>
          {isUploading && (
            <div className="absolute inset-0 flex items-center justify-center bg-black/50">
              <Loader2 className="w-8 h-8 text-white animate-spin" />
            </div>
          )}
        </div>
      ) : (
        <label className="flex flex-col items-center justify-center w-full h-48 border-2 border-dashed border-gray-300 rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100 transition-colors">
          {isUploading ? (
            <div className="flex flex-col items-center justify-center pt-5 pb-6">
              <Loader2 className="w-8 h-8 text-primary animate-spin mb-2" />
              <p className="text-sm text-gray-500">Uploading image...</p>
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center pt-5 pb-6">
              <ImageIcon className="w-10 h-10 text-gray-400 mb-3" />
              <p className="mb-2 text-sm text-gray-500">
                <span className="font-semibold">Click to upload</span> or drag and drop
              </p>
              <p className="text-xs text-gray-500">PNG, JPG up to 10MB</p>
            </div>
          )}
          <input
            type="file"
            className="hidden"
            accept="image/*"
            onChange={handleFileChange}
            disabled={isUploading}
          />
        </label>
      )}
    </div>
  );
};

export default InventoryImageUploader;