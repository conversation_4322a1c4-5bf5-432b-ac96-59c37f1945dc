import React from 'react';
import { UniversalImageUploader } from '@/components/common/UniversalImageUploader';
import { toast } from 'sonner';

interface ImageUploaderProps {
  imageUrl: string;
  onImageChange: (url: string) => void;
  propertyId?: string;
}

const ImageUploader: React.FC<ImageUploaderProps> = ({
  imageUrl,
  onImageChange,
  propertyId
}) => {
  const handleUploadComplete = (url: string) => {
    onImageChange(url);
    toast.success('Property image uploaded successfully');
  };

  const handleUploadError = (error: string) => {
    toast.error(`Failed to upload image: ${error}`);
  };

  const handleRemoveImage = () => {
    onImageChange('');
    toast.success('Image removed');
  };

  return (
    <div className="w-full">
      <UniversalImageUploader
        config={{
          bucketName: 'property-images',
          folderPath: propertyId || 'temp',
          metadata: { propertyId, entityId: propertyId },
        }}
        imageUrl={imageUrl}
        onUploadComplete={handleUploadComplete}
        onUploadError={handleUploadError}
        showDropZone={true}
        showPreview={true}
        uploadButtonText="Upload Property Image"
        showCompressionInfo={true}
        className="w-full"
      />

      {imageUrl && (
        <div className="mt-2 flex justify-end">
          <button
            type="button"
            onClick={handleRemoveImage}
            className="text-sm text-red-600 hover:text-red-800 underline"
          >
            Remove Image
          </button>
        </div>
      )}
    </div>
  );
};

export default ImageUploader;