import React, { useState } from 'react';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { AuthState, UserProfile } from '@/types/auth';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { useAuth } from '@/contexts/AuthContext';
import { Camera, Loader2, Upload, X } from 'lucide-react';
import { UniversalImageUploader } from '@/components/common/UniversalImageUploader';

interface ProfileSettingsProps {
  profile: AuthState['profile'];
}

const ProfileSettings = ({ profile }: ProfileSettingsProps) => {
  const { refreshProfile } = useAuth();
  const [firstName, setFirstName] = useState(profile?.first_name || '');
  const [lastName, setLastName] = useState(profile?.last_name || '');
  const [saving, setSaving] = useState(false);
  const [avatarUrl, setAvatarUrl] = useState(profile?.avatar_url || '');

  const handleAvatarUploadComplete = async (url: string) => {
    try {
      // Update the profile with the new avatar URL
      const { error: updateError } = await supabase
        .from('profiles')
        .update({ avatar_url: url })
        .eq('id', profile?.id);

      if (updateError) {
        throw updateError;
      }

      // Update local state
      setAvatarUrl(url);

      // Refresh the profile in the auth context
      await refreshProfile();

      toast.success('Avatar updated successfully');
    } catch (error) {
      console.error('Error updating profile:', error);
      toast.error('Failed to update profile');
    }
  };

  const handleAvatarUploadError = (error: string) => {
    console.error('Avatar upload error:', error);
    toast.error(`Failed to upload avatar: ${error}`);
  };

  const handleRemoveAvatar = async () => {
    try {
      // Update the profile to remove the avatar URL
      const { error: updateError } = await supabase
        .from('profiles')
        .update({ avatar_url: null })
        .eq('id', profile?.id);

      if (updateError) {
        throw updateError;
      }

      // Update local state
      setAvatarUrl('');

      // Refresh the profile in the auth context
      await refreshProfile();

      toast.success('Avatar removed successfully');
    } catch (error) {
      console.error('Error removing avatar:', error);
      toast.error('Failed to remove avatar');
    }
  };

  const handleSaveProfile = async () => {
    try {
      setSaving(true);

      // Update the profile with the new name
      const { error } = await supabase
        .from('profiles')
        .update({
          first_name: firstName,
          last_name: lastName,
        })
        .eq('id', profile?.id);

      if (error) {
        throw error;
      }

      // Refresh the profile in the auth context
      await refreshProfile();

      toast.success('Profile updated successfully');
    } catch (error) {
      console.error('Error updating profile:', error);
      toast.error('Failed to update profile');
    } finally {
      setSaving(false);
    }
  };

  return (
    <div className="space-y-8">
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Profile Picture</h3>
        <div className="flex items-center gap-6">
          <div className="relative">
            <Avatar className="h-24 w-24 border-2 border-border">
              <AvatarImage src={avatarUrl} alt={profile?.first_name || 'User'} />
              <AvatarFallback className="text-2xl">
                {profile?.first_name?.charAt(0) || 'U'}
              </AvatarFallback>
            </Avatar>
          </div>

          <div className="flex-1 max-w-md">
            <UniversalImageUploader
              config={{
                bucketName: 'avatar-images',
                folderPath: profile?.id || 'temp',
                fileName: `avatar-${Date.now()}`,
                maxWidth: 512,
                maxHeight: 512,
                metadata: { entityId: profile?.id },
              }}
              imageUrl={avatarUrl}
              onUploadComplete={handleAvatarUploadComplete}
              onUploadError={handleAvatarUploadError}
              showDropZone={false}
              showPreview={false}
              uploadButtonText="Upload New Avatar"
              className="space-y-2"
            />

            {avatarUrl && (
              <Button
                variant="outline"
                size="sm"
                className="flex items-center gap-2 text-destructive hover:text-destructive mt-2"
                onClick={handleRemoveAvatar}
              >
                <X className="h-4 w-4" />
                Remove Avatar
              </Button>
            )}

            <p className="text-xs text-muted-foreground mt-2">
              Recommended: Square image, automatically resized to 512x512. Supports WebP compression for optimal file size.
            </p>
          </div>
        </div>
      </div>

      <div className="space-y-4">
        <h3 className="text-lg font-medium">Personal Information</h3>
        <div className="grid gap-4 grid-cols-1 md:grid-cols-2">
          <div className="space-y-2">
            <Label htmlFor="firstName">First Name</Label>
            <Input
              id="firstName"
              value={firstName}
              onChange={(e) => setFirstName(e.target.value)}
              placeholder="Enter your first name"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="lastName">Last Name</Label>
            <Input
              id="lastName"
              value={lastName}
              onChange={(e) => setLastName(e.target.value)}
              placeholder="Enter your last name"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              value={profile?.email || ''}
              disabled
              className="bg-muted"
            />
            <p className="text-xs text-muted-foreground">
              Email cannot be changed
            </p>
          </div>
          <div className="space-y-2">
            <Label htmlFor="role">Account Type</Label>
            <Input
              id="role"
              value={
                profile?.role === 'property_manager' ? 'Property Manager' :
                profile?.role === 'super_admin' ? 'Super Admin' :
                profile?.role === 'admin' ? 'Admin' :
                profile?.role === 'service_provider' ? 'Service Provider' :
                profile?.role === 'staff' ? 'Staff' :
                profile?.role || 'User'
              }
              disabled
              className="bg-muted"
            />
          </div>
        </div>

        <div className="flex justify-end mt-4">
          <Button
            onClick={handleSaveProfile}
            disabled={saving}
            className="flex items-center gap-2"
          >
            {saving && <Loader2 className="h-4 w-4 animate-spin" />}
            Save Profile
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ProfileSettings;
