import React, { useState, useCallback, useRef } from 'react';
import { Upload, ImageIcon, Loader2, X, CheckCircle, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { cn } from '@/lib/utils';
import { useUniversalImageUpload, UploadConfig } from '@/hooks/useUniversalImageUpload';
import { formatFileSize } from '@/utils/imageProcessing';

export interface UniversalImageUploaderProps {
  /** Upload configuration */
  config: UploadConfig;
  /** Current image URL to display */
  imageUrl?: string;
  /** Callback when upload completes successfully */
  onUploadComplete?: (url: string) => void;
  /** Callback when upload fails */
  onUploadError?: (error: string) => void;
  /** Custom className for styling */
  className?: string;
  /** Show drag and drop zone */
  showDropZone?: boolean;
  /** Show image preview */
  showPreview?: boolean;
  /** Custom upload button text */
  uploadButtonText?: string;
  /** Disabled state */
  disabled?: boolean;
  /** Accept specific file types */
  accept?: string;
  /** Show compression info */
  showCompressionInfo?: boolean;
}

export const UniversalImageUploader: React.FC<UniversalImageUploaderProps> = ({
  config,
  imageUrl,
  onUploadComplete,
  onUploadError,
  className,
  showDropZone = true,
  showPreview = true,
  uploadButtonText = 'Upload Image',
  disabled = false,
  accept = 'image/*',
  showCompressionInfo = true,
}) => {
  const [isDragOver, setIsDragOver] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(imageUrl || null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const {
    uploadImage,
    isUploading,
    progress,
    error,
    originalSize,
    processedSize,
    compressionRatio,
    resetUploadState,
  } = useUniversalImageUpload();

  const handleFileSelect = useCallback(async (file: File) => {
    if (!file || disabled) return;

    // Create preview URL
    const preview = URL.createObjectURL(file);
    setPreviewUrl(preview);

    try {
      const result = await uploadImage(file, {
        ...config,
        onProgress: config.onProgress,
      });

      if (result.success && result.url) {
        onUploadComplete?.(result.url);
        // Clean up preview URL since we have the final URL
        URL.revokeObjectURL(preview);
        setPreviewUrl(result.url);
      } else {
        onUploadError?.(result.error || 'Upload failed');
        // Keep preview URL for user to see what they tried to upload
      }
    } catch (err: any) {
      onUploadError?.(err.message || 'Upload failed');
    }
  }, [uploadImage, config, onUploadComplete, onUploadError, disabled]);

  const handleFileInputChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  }, [handleFileSelect]);

  const handleDrop = useCallback((event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    setIsDragOver(false);

    if (disabled) return;

    const files = event.dataTransfer.files;
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  }, [handleFileSelect, disabled]);

  const handleDragOver = useCallback((event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    if (!disabled) {
      setIsDragOver(true);
    }
  }, [disabled]);

  const handleDragLeave = useCallback((event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleRemoveImage = useCallback(() => {
    if (previewUrl) {
      URL.revokeObjectURL(previewUrl);
    }
    setPreviewUrl(null);
    resetUploadState();
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, [previewUrl, resetUploadState]);

  const handleUploadClick = useCallback(() => {
    fileInputRef.current?.click();
  }, []);

  return (
    <div className={cn('space-y-4', className)}>
      {/* Upload Area */}
      {showDropZone && (
        <div
          className={cn(
            'border-2 border-dashed rounded-lg p-6 text-center transition-colors',
            isDragOver && !disabled
              ? 'border-primary bg-primary/5'
              : 'border-muted-foreground/25 hover:border-muted-foreground/50',
            disabled && 'opacity-50 cursor-not-allowed'
          )}
          onDrop={handleDrop}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
        >
          <div className="flex flex-col items-center gap-2">
            {isUploading ? (
              <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
            ) : (
              <Upload className="h-8 w-8 text-muted-foreground" />
            )}
            
            <div className="space-y-1">
              <p className="text-sm font-medium">
                {isUploading ? 'Uploading...' : 'Drop your image here'}
              </p>
              <p className="text-xs text-muted-foreground">
                or click to browse (max 10MB)
              </p>
            </div>

            <Button
              variant="outline"
              size="sm"
              onClick={handleUploadClick}
              disabled={disabled || isUploading}
              className="mt-2"
            >
              {isUploading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Uploading...
                </>
              ) : (
                <>
                  <ImageIcon className="h-4 w-4 mr-2" />
                  {uploadButtonText}
                </>
              )}
            </Button>
          </div>
        </div>
      )}

      {/* Simple Upload Button (when dropzone is disabled) */}
      {!showDropZone && (
        <Button
          variant="outline"
          onClick={handleUploadClick}
          disabled={disabled || isUploading}
          className="w-full"
        >
          {isUploading ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Uploading...
            </>
          ) : (
            <>
              <ImageIcon className="h-4 w-4 mr-2" />
              {uploadButtonText}
            </>
          )}
        </Button>
      )}

      {/* Hidden File Input */}
      <input
        ref={fileInputRef}
        type="file"
        accept={accept}
        onChange={handleFileInputChange}
        className="hidden"
        disabled={disabled}
      />

      {/* Progress Bar */}
      {isUploading && (
        <div className="space-y-2">
          <Progress value={progress} className="w-full" />
          <p className="text-xs text-muted-foreground text-center">
            {progress}% complete
          </p>
        </div>
      )}

      {/* Image Preview */}
      {showPreview && previewUrl && (
        <div className="relative">
          <div className="relative rounded-lg overflow-hidden border">
            <img
              src={previewUrl}
              alt="Preview"
              className="w-full h-48 object-cover"
            />
            
            {/* Remove Button */}
            <Button
              variant="destructive"
              size="sm"
              className="absolute top-2 right-2"
              onClick={handleRemoveImage}
              disabled={isUploading}
            >
              <X className="h-4 w-4" />
            </Button>

            {/* Upload Status Overlay */}
            {isUploading && (
              <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
                <div className="text-white text-center">
                  <Loader2 className="h-8 w-8 animate-spin mx-auto mb-2" />
                  <p className="text-sm">Processing...</p>
                </div>
              </div>
            )}
          </div>

          {/* Compression Info */}
          {showCompressionInfo && originalSize && processedSize && !isUploading && (
            <div className="mt-2 p-2 bg-muted rounded text-xs">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <span>
                  Compressed: {formatFileSize(originalSize)} → {formatFileSize(processedSize)}
                  {compressionRatio && compressionRatio > 0 && (
                    <span className="text-green-600 ml-1">
                      ({compressionRatio}% smaller)
                    </span>
                  )}
                </span>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Error Display */}
      {error && (
        <div className="flex items-center gap-2 p-3 bg-destructive/10 border border-destructive/20 rounded-lg">
          <AlertCircle className="h-4 w-4 text-destructive" />
          <p className="text-sm text-destructive">{error}</p>
        </div>
      )}
    </div>
  );
};

export default UniversalImageUploader;
